import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/components/Layout.vue'
import Home from '@/views/Home.vue'
import Login from '@/views/Login.vue'
import Register from '@/views/Register.vue'
import Profile from '@/views/Profile.vue'
import NotFound from '@/views/NotFound.vue'

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Home',
        component: Home
      },
      {
        path: 'circle',
        name: 'Circle',
        component: Home // 临时使用 Home 组件
      },
      {
        path: 'tools',
        name: 'Tools',
        component: Home // 临时使用 Home 组件
      },
      {
        path: 'tutorial',
        name: 'Tutorial',
        component: Home // 临时使用 Home 组件
      },
      {
        path: 'qa',
        name: 'QA',
        component: Home // 临时使用 Home 组件
      },
      {
        path: 'jobs',
        name: 'Jobs',
        component: Home // 临时使用 Home 组件
      },
      {
        path: 'profile',
        name: 'Profile',
        component: Profile
      }
    ]
  },
  // 登录和注册页面（不使用Layout布局）
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/register',
    name: 'Register',
    component: Register
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound
  }
]
