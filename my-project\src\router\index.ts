import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/components/Layout.vue'
import Home from '@/views/Home.vue'
import NotFound from '@/views/NotFound.vue'

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Home',
        component: Home
      },
      {
        path: 'circle',
        name: 'Circle',
        component: Home // 临时使用 Home 组件
      },
      {
        path: 'tools',
        name: 'Tools',
        component: Home // 临时使用 Home 组件
      },
      {
        path: 'tutorial',
        name: 'Tutorial',
        component: Home // 临时使用 Home 组件
      },
      {
        path: 'qa',
        name: 'QA',
        component: Home // 临时使用 Home 组件
      },
      {
        path: 'jobs',
        name: 'Jobs',
        component: Home // 临时使用 Home 组件
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound
  }
]
