{"name": "my-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 8000", "build": "vue-tsc && vite build", "preview": "vite preview --port 8000", "type-check": "vue-tsc --noEmit"}, "dependencies": {"axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.10.4", "mitt": "^3.0.1", "pinia": "^3.0.3", "vue": "^3.4.0", "vue-router": "^4.2.0", "vuex": "^4.1.0", "vuex-along": "^1.2.13"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "@vue/tsconfig": "^0.5.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}}