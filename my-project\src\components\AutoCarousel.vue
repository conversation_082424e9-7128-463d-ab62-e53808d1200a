<template>
  <div class="auto-carousel">
    <div class="carousel-container">
      <div 
        class="carousel-wrapper"
        :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
      >
        <div 
          v-for="(slide, index) in slides" 
          :key="index"
          class="carousel-slide"
          :style="{ backgroundImage: `url(${slide.image})` }"
        >
          <div class="slide-overlay"></div>
          <div class="slide-content">
            <h1 class="slide-title">{{ slide.title }}</h1>
            <p class="slide-subtitle">{{ slide.subtitle }}</p>
            <div class="slide-actions" v-if="slide.actions">
              <button 
                v-for="action in slide.actions" 
                :key="action.text"
                :class="['btn', action.type]"
                @click="action.handler"
              >
                {{ action.text }}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 进度条 -->
      <div class="carousel-progress">
        <div
          class="progress-bar"
          :style="{ width: `${((currentIndex + 1) / slides.length) * 100}%` }"
        ></div>
      </div>

      <!-- 指示器 -->
      <div class="carousel-indicators">
        <div
          v-for="(slide, index) in slides"
          :key="index"
          :class="['indicator', { active: index === currentIndex }]"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface SlideAction {
  text: string
  type: string
  handler: () => void
}

interface Slide {
  title: string
  subtitle: string
  image: string
  actions?: SlideAction[]
}

// 轮播图数据
const slides = ref<Slide[]>([
  {
    title: '欢迎来到知识乐园',
    subtitle: '基于 Vue 3 + TypeScript + Vite 构建的现代化前端项目',
    image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=400&q=80',
    actions: [
      {
        text: '开始使用',
        type: 'btn-primary',
        handler: () => console.log('开始使用')
      },
      {
        text: '了解更多',
        type: 'btn-secondary',
        handler: () => console.log('了解更多')
      }
    ]
  },
  {
    title: '探索知识的海洋',
    subtitle: '与志同道合的朋友一起学习、分享、成长',
    image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=400&q=80',
    actions: [
      {
        text: '加入圈子',
        type: 'btn-primary',
        handler: () => console.log('加入圈子')
      },
      {
        text: '浏览话题',
        type: 'btn-secondary',
        handler: () => console.log('浏览话题')
      }
    ]
  },
  {
    title: '分享你的见解',
    subtitle: '在这里发表文章、参与讨论、展示你的专业知识',
    image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=400&q=80',
    actions: [
      {
        text: '立即发布',
        type: 'btn-primary',
        handler: () => console.log('立即发布')
      },
      {
        text: '查看示例',
        type: 'btn-secondary',
        handler: () => console.log('查看示例')
      }
    ]
  },
  {
    title: '构建你的影响力',
    subtitle: '通过优质内容获得认可，建立个人品牌',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=400&q=80',
    actions: [
      {
        text: '创建档案',
        type: 'btn-primary',
        handler: () => console.log('创建档案')
      },
      {
        text: '查看排行',
        type: 'btn-secondary',
        handler: () => console.log('查看排行')
      }
    ]
  }
])

// 当前轮播索引
const currentIndex = ref(0)
let autoPlayTimer: number | null = null

// 自动播放间隔（毫秒）
const autoPlayInterval = 4000

// 下一张
const nextSlide = () => {
  currentIndex.value = (currentIndex.value + 1) % slides.value.length
}

// 开始自动播放
const startAutoPlay = () => {
  if (autoPlayTimer) {
    clearInterval(autoPlayTimer)
  }
  autoPlayTimer = setInterval(nextSlide, autoPlayInterval)
}

// 停止自动播放
const stopAutoPlay = () => {
  if (autoPlayTimer) {
    clearInterval(autoPlayTimer)
    autoPlayTimer = null
  }
}

// 组件挂载时开始自动播放
onMounted(() => {
  startAutoPlay()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoPlay()
})
</script>

<style scoped>
.auto-carousel {
  width: 100%;
  height: 400px;
  position: relative;
  overflow: hidden;
}

.carousel-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.carousel-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.carousel-slide {
  flex: 0 0 100%;
  height: 100%;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.85) 0%, rgba(118, 75, 162, 0.85) 100%);
  backdrop-filter: blur(1px);
}

.slide-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.slide-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  opacity: 0;
  transform: translateY(30px);
  animation: slideInUp 0.8s ease-out 0.3s forwards;
}

.slide-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  opacity: 0;
  transform: translateY(30px);
  animation: slideInUp 0.8s ease-out 0.5s forwards;
}

.slide-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  opacity: 0;
  transform: translateY(30px);
  animation: slideInUp 0.8s ease-out 0.7s forwards;
}

.btn {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #ffffff;
  color: #667eea;
}

.btn-primary:hover {
  background-color: #f8fafc;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.btn-secondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background-color: white;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

/* 进度条 */
.carousel-progress {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  z-index: 3;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
  transition: width 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* 指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
  z-index: 3;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background-color: white;
  transform: scale(1.2);
}

/* 动画 */
@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auto-carousel {
    height: 300px;
  }
  
  .slide-title {
    font-size: 2rem;
  }
  
  .slide-subtitle {
    font-size: 1rem;
  }
  
  .slide-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 200px;
  }
}
</style>
