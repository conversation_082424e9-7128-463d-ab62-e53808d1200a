<template>
  <div class="layout">
    <Header />
    <main class="main-content">
      <div class="content-container">
        <router-view />
      </div>
    </main>
    <Footer />
  </div>
</template>

<script setup lang="ts">
import Header from './Header.vue'
import Footer from './Footer.vue'
</script>

<style scoped>
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.main-content {
  flex: 1;
  width: 100%;
  background-color: #f8fafc;
}

.content-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem 1rem;
  min-height: calc(100vh - 120px); /* 减去 header(60px) + footer(60px) */
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content-container {
    max-width: 375px;
    padding: 1rem 0.75rem;
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .content-container {
    max-width: 768px;
    padding: 1.5rem 1rem;
  }
}
</style>
