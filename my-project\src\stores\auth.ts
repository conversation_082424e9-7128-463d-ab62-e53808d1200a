import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export interface User {
  id: string
  username: string
  email?: string
  avatar?: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))

  // 计算属性
  const isLoggedIn = computed(() => !!user.value && !!token.value)

  // 登录
  const login = (userData: User, authToken: string) => {
    user.value = userData
    token.value = authToken
    localStorage.setItem('auth_token', authToken)
    localStorage.setItem('user_data', JSON.stringify(userData))
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')
  }

  // 初始化用户数据（从本地存储恢复）
  const initializeAuth = () => {
    const savedToken = localStorage.getItem('auth_token')
    const savedUserData = localStorage.getItem('user_data')
    
    if (savedToken && savedUserData) {
      try {
        token.value = savedToken
        user.value = JSON.parse(savedUserData)
      } catch (error) {
        console.error('Failed to parse saved user data:', error)
        logout()
      }
    }
  }

  // 更新用户信息
  const updateUser = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      localStorage.setItem('user_data', JSON.stringify(user.value))
    }
  }

  return {
    user,
    token,
    isLoggedIn,
    login,
    logout,
    initializeAuth,
    updateUser
  }
})
