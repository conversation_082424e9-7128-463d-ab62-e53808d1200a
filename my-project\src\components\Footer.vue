<template>
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-content">
        <!-- 左侧信息 -->
        <div class="footer-left">
          <div class="footer-logo">
            <img src="https://picsum.photos/32/32?random=2" alt="Logo" class="footer-logo-img" />
            <span class="footer-logo-text">知识乐园</span>
          </div>
          <p class="footer-description">
            基于 Vue 3 + TypeScript + Vite 构建的现代化前端项目
          </p>
        </div>

        <!-- 中间链接 -->
        <div class="footer-links">
          <div class="link-group">
            <h4 class="link-title">产品</h4>
            <ul class="link-list">
              <li><a href="#" class="link-item">功能介绍</a></li>
              <li><a href="#" class="link-item">使用指南</a></li>
              <li><a href="#" class="link-item">更新日志</a></li>
            </ul>
          </div>
          
          <div class="link-group">
            <h4 class="link-title">支持</h4>
            <ul class="link-list">
              <li><a href="#" class="link-item">帮助中心</a></li>
              <li><a href="#" class="link-item">联系我们</a></li>
              <li><a href="#" class="link-item">反馈建议</a></li>
            </ul>
          </div>
          
          <div class="link-group">
            <h4 class="link-title">关于</h4>
            <ul class="link-list">
              <li><a href="#" class="link-item">关于我们</a></li>
              <li><a href="#" class="link-item">隐私政策</a></li>
              <li><a href="#" class="link-item">服务条款</a></li>
            </ul>
          </div>
        </div>

        <!-- 右侧社交媒体 -->
        <div class="footer-right">
          <h4 class="social-title">关注我们</h4>
          <div class="social-links">
            <a href="#" class="social-link" title="GitHub">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
            </a>
            <a href="#" class="social-link" title="微博">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15c-2.5 0-4.5-2-4.5-4.5S7.5 8 10 8s4.5 2 4.5 4.5S12.5 17 10 17z"/>
              </svg>
            </a>
            <a href="#" class="social-link" title="微信">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.5 12c-.83 0-1.5-.67-1.5-1.5S7.67 9 8.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm7 0c-.83 0-1.5-.67-1.5-1.5S14.67 9 15.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm3.5-1.5c0 2.5-2.91 4.5-6.5 4.5-1.4 0-2.7-.35-3.76-.95L4 16l1.95-1.26C5.35 13.7 5 12.4 5 11c0-2.5 2.91-4.5 6.5-4.5S18 8.5 18 11z"/>
              </svg>
            </a>
          </div>
        </div>
      </div>

      <!-- 底部版权信息 -->
      <div class="footer-bottom">
        <div class="copyright">
          <p>&copy; 2025 知识乐园. All rights reserved.</p>
        </div>
        <div class="footer-meta">
          <span>ICP备案号：京ICP备12345678号</span>
          <span class="divider">|</span>
          <span>版本 v1.0.0</span>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
// Footer 组件逻辑
</script>

<style scoped>
.footer {
  background-color: #1f2937;
  color: #d1d5db;
  margin-top: auto;
}

.footer-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 3rem 1rem 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

/* 左侧信息 */
.footer-left {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.footer-logo-img {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.footer-logo-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: #ffffff;
}

.footer-description {
  color: #9ca3af;
  line-height: 1.6;
  font-size: 0.875rem;
}

/* 中间链接 */
.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.link-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.link-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.link-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.link-item {
  color: #9ca3af;
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.2s ease;
}

.link-item:hover {
  color: #3b82f6;
}

/* 右侧社交媒体 */
.footer-right {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.social-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #374151;
  color: #9ca3af;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.2s ease;
}

.social-link:hover {
  background-color: #3b82f6;
  color: #ffffff;
  transform: translateY(-2px);
}

/* 底部版权 */
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid #374151;
  font-size: 0.875rem;
}

.copyright {
  color: #9ca3af;
}

.footer-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #6b7280;
}

.divider {
  color: #4b5563;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .footer-container {
    padding: 2rem 0.75rem 1rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .footer-links {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .social-links {
    justify-content: center;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
  
  .footer-right {
    grid-column: 1 / -1;
    text-align: center;
  }
  
  .social-links {
    justify-content: center;
  }
}
</style>
