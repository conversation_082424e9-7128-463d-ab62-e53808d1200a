# My Project

基于 Vue 3 + TypeScript + Vite 构建的现代化前端项目框架。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - JavaScript 的超集，提供静态类型检查
- **Vite** - 下一代前端构建工具
- **Vue Router 4** - Vue.js 官方路由管理器

## 项目特性

- ⚡ 极速的热重载开发体验
- 🔧 完整的 TypeScript 支持
- 📱 响应式设计，适配 PC 端
- 🎨 现代化的 UI 设计
- 🛠️ 开箱即用的项目配置

## 快速开始

### 环境要求

- Node.js 20+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

项目将在 http://localhost:8000 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

### 类型检查

```bash
npm run type-check
```

## 项目结构

```
my-project/
├── public/                 # 静态资源
├── src/
│   ├── views/             # 页面组件
│   │   ├── Home.vue       # 首页
│   │   └── NotFound.vue   # 404页面
│   ├── router/            # 路由配置
│   │   └── index.ts       # 路由定义
│   ├── App.vue            # 根组件
│   ├── main.ts            # 应用入口
│   └── style.css          # 全局样式
├── index.html             # HTML 模板
├── vite.config.ts         # Vite 配置
├── tsconfig.json          # TypeScript 配置
└── package.json           # 项目配置
```

## 开发说明

- 默认端口：8000
- 支持热重载
- 已配置路径别名 `@` 指向 `src` 目录
- 包含基础的首页和404页面
- 响应式设计，适配不同屏幕尺寸

## 许可证

MIT License
