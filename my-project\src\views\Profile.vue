<template>
  <div class="profile-page">
    <div class="profile-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">基础信息</h1>
        <button class="save-btn" @click="saveProfile" :disabled="isSaving">
          <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"/>
          </svg>
          {{ isSaving ? '保存中...' : '保存信息' }}
        </button>
      </div>

      <!-- 个人信息表单 -->
      <div class="profile-form">
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">名称</label>
            <div class="form-content">{{ profileData.name || '3123' }}</div>
          </div>
          <div class="form-actions">
            <!-- 操作按钮区域 -->
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">商户UID</label>
            <div class="form-content">{{ profileData.merchantUid || '868568581279246' }}</div>
          </div>
          <div class="form-actions">
            <button class="action-btn copy-btn" @click="copyToClipboard(profileData.merchantUid)">
              复制
            </button>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">商家名</label>
            <div class="form-content">{{ profileData.merchantName || '黄瓜+758' }}</div>
          </div>
          <div class="form-actions">
            <!-- 操作按钮区域 -->
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">商家Logo</label>
            <div class="form-content logo-content">
              <div class="logo-placeholder" v-if="!profileData.logo">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                  <circle cx="8.5" cy="8.5" r="1.5"/>
                  <polyline points="21,15 16,10 5,21"/>
                </svg>
              </div>
              <img v-else :src="profileData.logo" alt="商家Logo" class="logo-image">
            </div>
          </div>
          <div class="form-actions">
            <!-- 操作按钮区域 -->
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">联系人</label>
            <input 
              v-model="profileData.contactPerson" 
              type="text" 
              class="form-input"
              placeholder="请输入联系人姓名"
            />
          </div>
          <div class="form-actions">
            <!-- 操作按钮区域 -->
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">联系电话</label>
            <input 
              v-model="profileData.contactPhone" 
              type="tel" 
              class="form-input"
              placeholder="请输入联系电话"
            />
          </div>
          <div class="form-actions">
            <!-- 操作按钮区域 -->
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">推送邮箱</label>
            <input 
              v-model="profileData.pushEmail" 
              type="email" 
              class="form-input"
              placeholder="请输入推送邮箱"
            />
          </div>
          <div class="form-actions">
            <!-- 操作按钮区域 -->
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">营业时间</label>
            <div class="form-content">{{ profileData.businessHours || '2025-07-08 ~ 2026-07-08' }}</div>
          </div>
          <div class="form-actions">
            <!-- 操作按钮区域 -->
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">微信群</label>
            <input 
              v-model="profileData.wechatGroup" 
              type="text" 
              class="form-input"
              placeholder="请输入微信群信息"
            />
          </div>
          <div class="form-actions">
            <!-- 操作按钮区域 -->
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">对外标签</label>
            <input 
              v-model="profileData.externalTags" 
              type="text" 
              class="form-input"
              placeholder="请输入对外标签"
            />
          </div>
          <div class="form-actions">
            <!-- 操作按钮区域 -->
          </div>
        </div>

        <!-- 退回按钮 -->
        <div class="form-footer">
          <button type="button" class="back-btn" @click="goBack">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            退回
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 个人资料数据
const profileData = ref({
  name: '3123',
  merchantUid: '868568581279246',
  merchantName: '黄瓜+758',
  logo: '',
  contactPerson: '',
  contactPhone: '',
  pushEmail: '',
  businessHours: '2025-07-08 ~ 2026-07-08',
  wechatGroup: '',
  externalTags: ''
})

// 保存状态
const isSaving = ref(false)

// 保存个人资料
const saveProfile = async () => {
  isSaving.value = true
  
  try {
    // 这里添加保存逻辑
    console.log('保存个人资料:', profileData.value)
    
    // 模拟保存请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    alert('保存成功！')
  } catch (error) {
    console.error('保存失败:', error)
    alert('保存失败，请重试')
  } finally {
    isSaving.value = false
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    alert('复制成功！')
  } catch (error) {
    console.error('复制失败:', error)
    alert('复制失败，请手动复制')
  }
}

// 退回到主页
const goBack = () => {
  router.push('/')
}

// 组件挂载时加载数据
onMounted(() => {
  // 这里可以添加从服务器加载数据的逻辑
  console.log('个人中心页面已加载')
})
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 2rem;
}

.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 页面标题 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.save-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.save-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 表单样式 */
.profile-form {
  padding: 2rem;
}

.form-row {
  display: flex;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.form-row:last-child {
  border-bottom: none;
}

.form-group {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.form-label {
  width: 120px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;
  flex-shrink: 0;
}

.form-content {
  flex: 1;
  font-size: 0.875rem;
  color: #333;
  min-height: 20px;
}

.form-input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input::placeholder {
  color: #aaa;
}

.form-actions {
  width: 100px;
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 0.375rem 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.copy-btn {
  color: #667eea;
  border-color: #667eea;
}

.copy-btn:hover {
  background: #667eea;
  color: white;
}

/* Logo 相关样式 */
.logo-content {
  display: flex;
  align-items: center;
}

.logo-placeholder {
  width: 40px;
  height: 40px;
  background: #f5f5f5;
  border: 2px dashed #ddd;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #aaa;
}

.logo-placeholder svg {
  width: 20px;
  height: 20px;
}

.logo-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 6px;
}

/* 底部区域 */
.form-footer {
  padding: 2rem 0 1rem 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 1rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #6b7280;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.back-btn:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

.back-btn svg {
  width: 16px;
  height: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-page {
    padding: 1rem;
  }
  
  .page-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .profile-form {
    padding: 1rem;
  }
  
  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .form-group {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .form-label {
    width: auto;
  }
  
  .form-actions {
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
