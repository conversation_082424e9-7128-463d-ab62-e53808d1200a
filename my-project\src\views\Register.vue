<template>
  <div class="register-page">
    <div class="register-container">
      <div class="register-form">
        <!-- 返回主页按钮 -->
        <button type="button" class="back-home-btn" @click="goToHome">
          <svg class="back-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          返回主页
        </button>

        <h2 class="form-title">注册账号</h2>
        
        <form @submit.prevent="handleRegister">
          <!-- 账号输入 -->
          <div class="form-group">
            <label for="username">账号</label>
            <input
              id="username"
              v-model="registerForm.username"
              type="text"
              placeholder="请输入用户名或邮箱"
              required
            />
          </div>

          <!-- 邮箱输入 -->
          <div class="form-group">
            <label for="email">邮箱</label>
            <input
              id="email"
              v-model="registerForm.email"
              type="email"
              placeholder="请输入邮箱地址"
              required
            />
          </div>

          <!-- 性别选择 -->
          <div class="form-group">
            <label>性别</label>
            <div class="gender-options">
              <label class="radio-label">
                <input
                  v-model="registerForm.gender"
                  type="radio"
                  value="male"
                />
                <span class="radio-mark"></span>
                男
              </label>
              <label class="radio-label">
                <input
                  v-model="registerForm.gender"
                  type="radio"
                  value="female"
                />
                <span class="radio-mark"></span>
                女
              </label>
              <label class="radio-label">
                <input
                  v-model="registerForm.gender"
                  type="radio"
                  value="other"
                />
                <span class="radio-mark"></span>
                其他
              </label>
            </div>
          </div>

          <!-- 密码输入 -->
          <div class="form-group">
            <label for="password">密码</label>
            <input
              id="password"
              v-model="registerForm.password"
              type="password"
              placeholder="请输入密码"
              required
            />
          </div>

          <!-- 确认密码 -->
          <div class="form-group">
            <label for="confirmPassword">确认密码</label>
            <input
              id="confirmPassword"
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              required
            />
          </div>

          <!-- 服务条款 -->
          <div class="form-options">
            <label class="checkbox-label">
              <input
                v-model="registerForm.agreeTerms"
                type="checkbox"
                required
              />
              <span class="checkmark"></span>
              我已阅读并同意
              <a href="#" class="terms-link">《用户服务协议》</a>
            </label>
          </div>

          <!-- 注册按钮 -->
          <button type="submit" class="register-btn" :disabled="isLoading">
            <span v-if="isLoading">注册中...</span>
            <span v-else>注册账号</span>
          </button>
        </form>

        <!-- 登录链接 -->
        <div class="login-link">
          已有账号？
          <router-link to="/login" class="link">立即登录</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 表单数据
const registerForm = ref({
  username: '',
  email: '',
  gender: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 加载状态
const isLoading = ref(false)

// 注册处理
const handleRegister = async () => {
  // 表单验证
  if (!registerForm.value.username || !registerForm.value.email || 
      !registerForm.value.password || !registerForm.value.confirmPassword) {
    alert('请填写完整的注册信息')
    return
  }

  if (registerForm.value.password !== registerForm.value.confirmPassword) {
    alert('两次输入的密码不一致')
    return
  }

  if (!registerForm.value.agreeTerms) {
    alert('请同意用户服务协议')
    return
  }

  isLoading.value = true
  
  try {
    // 这里添加实际的注册逻辑
    console.log('注册信息:', registerForm.value)
    
    // 模拟注册请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    alert('注册成功！')
    // 注册成功后跳转到登录页面
    router.push('/login')
  } catch (error) {
    console.error('注册失败:', error)
    alert('注册失败，请重试')
  } finally {
    isLoading.value = false
  }
}

// 返回主页
const goToHome = () => {
  router.push('/')
}
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.register-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.register-container {
  position: relative;
  z-index: 1;
}

.register-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 3rem 2.5rem;
  width: 450px;
  max-width: 90vw;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

/* 返回主页按钮 */
.back-home-btn {
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  border: none;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.back-home-btn:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  transform: translateX(-2px);
}

.back-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.back-home-btn:hover .back-icon {
  transform: translateX(-2px);
}

.form-title {
  text-align: center;
  font-size: 1.75rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"] {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s;
  background: #f9fafb;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input::placeholder {
  color: #9ca3af;
}

/* 性别选择样式 */
.gender-options {
  display: flex;
  gap: 1.5rem;
  margin-top: 0.5rem;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.875rem;
  color: #6b7280;
}

.radio-label input[type="radio"] {
  display: none;
}

.radio-mark {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  margin-right: 0.5rem;
  position: relative;
  transition: all 0.2s;
}

.radio-label input[type="radio"]:checked + .radio-mark {
  border-color: #667eea;
}

.radio-label input[type="radio"]:checked + .radio-mark::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #667eea;
}

.form-options {
  margin-bottom: 2rem;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  margin-right: 0.5rem;
  margin-top: 2px;
  position: relative;
  transition: all 0.2s;
  flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #667eea;
  border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.terms-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.terms-link:hover {
  color: #5a67d8;
}

.register-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 1.5rem;
}

.register-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.register-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.login-link {
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
}

.login-link .link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.login-link .link:hover {
  color: #5a67d8;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .register-form {
    padding: 2rem 1.5rem;
    width: 100%;
  }
  
  .form-title {
    font-size: 1.5rem;
  }
  
  .gender-options {
    flex-direction: column;
    gap: 1rem;
  }
}
</style>
