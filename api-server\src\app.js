const express = require('express');
const cors = require('cors');
require('dotenv').config();

// 导入数据库连接
const { testConnection } = require('../config/database');

// 导入路由
const indexRoutes = require('../routes/index');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 8002;

// 中间件配置
app.use(cors()); // 启用CORS
app.use(express.json({ limit: '10mb' })); // 解析JSON请求体
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // 解析URL编码请求体

// 请求日志中间件
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.path}`);
  next();
});

// 路由配置
app.use('/api', indexRoutes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '欢迎使用API服务器',
    version: '1.0.0',
    endpoints: {
      health: '/api/health',
      info: '/api/info'
    }
  });
});

// 全局错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误：', error);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    console.log('🚀 正在启动API服务器...');
    const dbConnected = await testConnection();
    
    if (!dbConnected) {
      console.error('❌ 数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log('');
      console.log('🎉 API服务器启动成功！');
      console.log(`📡 服务器地址: http://localhost:${PORT}`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📊 健康检查: http://localhost:${PORT}/api/health`);
      console.log(`ℹ️  服务信息: http://localhost:${PORT}/api/info`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ 服务器启动失败：', error.message);
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

// 启动服务器
startServer();
