import type { CSSProperties } from 'vue';
export declare const classNameToArray: (cls?: string) => string[];
export declare const hasClass: (el: Element, cls: string) => boolean;
export declare const addClass: (el: Element, cls: string) => void;
export declare const removeClass: (el: Element, cls: string) => void;
export declare const getStyle: (element: HTMLElement, styleName: keyof CSSProperties) => string;
export declare const setStyle: (element: HTMLElement, styleName: CSSProperties | keyof CSSProperties, value?: string | number) => void;
export declare const removeStyle: (element: HTMLElement, style: CSSProperties | keyof CSSProperties) => void;
export declare function addUnit(value?: string | number, defaultUnit?: string): string | undefined;
