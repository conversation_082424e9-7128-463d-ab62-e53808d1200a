<template>
  <div class="login-page">
    <!-- 左侧科技背景区域 -->
    <div class="left-section">
      <div class="tech-background">
        <!-- 客户管理标题 -->
        <div class="header-title">客户管理</div>

        <!-- 墨界品牌标识 -->
        <div class="brand-logo">
          <div class="logo-icon">M</div>
          <div class="brand-text">墨界<br>Mojie</div>
        </div>

        <!-- 科技地球图形 -->
        <div class="earth-container">
          <div class="earth">
            <div class="orbit orbit-1"></div>
            <div class="orbit orbit-2"></div>
            <div class="orbit orbit-3"></div>
            <div class="hands">
              <div class="hand hand-left"></div>
              <div class="hand hand-right"></div>
            </div>
          </div>
        </div>

        <!-- 城市剪影 -->
        <div class="city-skyline">
          <div class="building" style="height: 60px; left: 10%;"></div>
          <div class="building" style="height: 80px; left: 15%;"></div>
          <div class="building" style="height: 45px; left: 20%;"></div>
          <div class="building" style="height: 90px; left: 25%;"></div>
          <div class="building" style="height: 70px; left: 30%;"></div>
          <div class="building" style="height: 55px; left: 35%;"></div>
          <div class="building" style="height: 85px; left: 40%;"></div>
          <div class="building" style="height: 65px; left: 45%;"></div>
          <div class="building" style="height: 75px; left: 50%;"></div>
          <div class="building" style="height: 50px; left: 55%;"></div>
          <div class="building" style="height: 95px; left: 60%;"></div>
          <div class="building" style="height: 40px; left: 65%;"></div>
          <div class="building" style="height: 80px; left: 70%;"></div>
          <div class="building" style="height: 60px; left: 75%;"></div>
          <div class="building" style="height: 70px; left: 80%;"></div>
          <div class="building" style="height: 55px; left: 85%;"></div>
          <div class="building" style="height: 65px; left: 90%;"></div>
        </div>
      </div>
    </div>

    <!-- 右侧登录表单区域 -->
    <div class="right-section">
      <div class="login-container">
        <div class="login-form">
          <h2 class="form-title">登录 墨界全域AI</h2>

          <form @submit.prevent="handleLogin">
            <!-- 手机号输入 -->
            <div class="form-group">
              <div class="input-wrapper">
                <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
                <input
                  id="username"
                  v-model="loginForm.username"
                  type="text"
                  placeholder="19286872073"
                  required
                />
              </div>
            </div>

            <!-- 密码输入 -->
            <div class="form-group">
              <div class="input-wrapper">
                <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                  <circle cx="12" cy="16" r="1"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                </svg>
                <input
                  id="password"
                  v-model="loginForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  placeholder="••••••••"
                  required
                />
                <button type="button" class="password-toggle" @click="togglePassword">
                  <svg v-if="showPassword" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                    <line x1="1" y1="1" x2="23" y2="23"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                  </svg>
                </button>
              </div>
            </div>

            <!-- 验证码输入 -->
            <div class="form-group">
              <div class="input-wrapper captcha-wrapper">
                <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                  <line x1="16" y1="2" x2="16" y2="6"/>
                  <line x1="8" y1="2" x2="8" y2="6"/>
                  <line x1="3" y1="10" x2="21" y2="10"/>
                </svg>
                <input
                  id="captcha"
                  v-model="loginForm.captcha"
                  type="text"
                  placeholder="请输入验证码"
                  required
                />
                <div class="captcha-image" @click="refreshCaptcha">
                  <canvas ref="captchaCanvas" width="80" height="32"></canvas>
                </div>
              </div>
            </div>

            <!-- 登录按钮 -->
            <button type="submit" class="login-btn" :disabled="isLoading">
              <span v-if="isLoading">登录中...</span>
              <span v-else>登录</span>
            </button>
          </form>

          <!-- 底部链接 -->
          <div class="form-footer">
            <button type="button" class="footer-link" @click="goToHome">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
              </svg>
              后台管理
            </button>
            <router-link to="/register" class="footer-link">
              返回首页
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"/>
              </svg>
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
  captcha: ''
})

// 状态
const isLoading = ref(false)
const showPassword = ref(false)
const captchaCanvas = ref<HTMLCanvasElement>()
const captchaCode = ref('')

// 切换密码显示
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 生成验证码
const generateCaptcha = () => {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789'
  let code = ''
  for (let i = 0; i < 4; i++) {
    code += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  captchaCode.value = code
  drawCaptcha(code)
}

// 绘制验证码
const drawCaptcha = (code: string) => {
  if (!captchaCanvas.value) return

  const canvas = captchaCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 设置背景
  ctx.fillStyle = '#f0f0f0'
  ctx.fillRect(0, 0, canvas.width, canvas.height)

  // 绘制干扰线
  for (let i = 0; i < 3; i++) {
    ctx.strokeStyle = `hsl(${Math.random() * 360}, 50%, 70%)`
    ctx.beginPath()
    ctx.moveTo(Math.random() * canvas.width, Math.random() * canvas.height)
    ctx.lineTo(Math.random() * canvas.width, Math.random() * canvas.height)
    ctx.stroke()
  }

  // 绘制验证码文字
  ctx.font = '16px Arial'
  ctx.textBaseline = 'middle'

  for (let i = 0; i < code.length; i++) {
    ctx.fillStyle = `hsl(${Math.random() * 360}, 70%, 40%)`
    ctx.save()
    ctx.translate(15 + i * 15, canvas.height / 2)
    ctx.rotate((Math.random() - 0.5) * 0.5)
    ctx.fillText(code[i], 0, 0)
    ctx.restore()
  }

  // 绘制干扰点
  for (let i = 0; i < 20; i++) {
    ctx.fillStyle = `hsl(${Math.random() * 360}, 50%, 60%)`
    ctx.beginPath()
    ctx.arc(Math.random() * canvas.width, Math.random() * canvas.height, 1, 0, 2 * Math.PI)
    ctx.fill()
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  generateCaptcha()
}

// 登录处理
const handleLogin = async () => {
  if (!loginForm.value.username || !loginForm.value.password || !loginForm.value.captcha) {
    alert('请填写完整的登录信息')
    return
  }

  if (loginForm.value.captcha.toLowerCase() !== captchaCode.value.toLowerCase()) {
    alert('验证码错误')
    refreshCaptcha()
    loginForm.value.captcha = ''
    return
  }

  isLoading.value = true

  try {
    // 这里添加实际的登录逻辑
    console.log('登录信息:', loginForm.value)

    // 模拟登录请求
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 登录成功后跳转到首页
    router.push('/')
  } catch (error) {
    console.error('登录失败:', error)
    alert('登录失败，请检查账号密码')
  } finally {
    isLoading.value = false
  }
}

// 返回主页
const goToHome = () => {
  router.push('/')
}

// 组件挂载后生成验证码
onMounted(() => {
  generateCaptcha()
})
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  background: #f5f5f5;
}

/* 左侧科技背景区域 */
.left-section {
  flex: 1;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 2rem;
}

.tech-background {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 客户管理标题 */
.header-title {
  color: white;
  font-size: 1.2rem;
  font-weight: 500;
  position: absolute;
  top: 2rem;
  left: 2rem;
}

/* 墨界品牌标识 */
.brand-logo {
  position: absolute;
  top: 2rem;
  right: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: white;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
}

.brand-text {
  font-size: 1rem;
  line-height: 1.2;
  font-weight: 500;
}

/* 科技地球 */
.earth-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
}

.earth {
  position: relative;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle at 30% 30%, #4fc3f7, #1976d2, #0d47a1);
  border-radius: 50%;
  margin: 75px auto;
  box-shadow:
    0 0 50px rgba(79, 195, 247, 0.3),
    inset -20px -20px 50px rgba(0, 0, 0, 0.2);
  animation: earthRotate 20s linear infinite;
}

.earth::before {
  content: '';
  position: absolute;
  top: 20%;
  left: 30%;
  width: 30%;
  height: 40%;
  background: rgba(76, 175, 80, 0.6);
  border-radius: 50% 30% 60% 40%;
  transform: rotate(-20deg);
}

.earth::after {
  content: '';
  position: absolute;
  top: 60%;
  right: 20%;
  width: 25%;
  height: 30%;
  background: rgba(76, 175, 80, 0.4);
  border-radius: 40% 60% 30% 50%;
  transform: rotate(30deg);
}

.orbit {
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: orbitRotate 15s linear infinite;
}

.orbit-1 {
  width: 200px;
  height: 200px;
  top: 50px;
  left: 50px;
  animation-duration: 10s;
}

.orbit-2 {
  width: 250px;
  height: 250px;
  top: 25px;
  left: 25px;
  animation-duration: 15s;
  animation-direction: reverse;
}

.orbit-3 {
  width: 300px;
  height: 300px;
  top: 0;
  left: 0;
  animation-duration: 20s;
}

/* 托举的手 */
.hands {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 100px;
}

.hand {
  position: absolute;
  width: 80px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 40px 40px 20px 20px;
}

.hand-left {
  left: 20px;
  transform: rotate(-15deg);
}

.hand-right {
  right: 20px;
  transform: rotate(15deg);
}

/* 城市剪影 */
.city-skyline {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120px;
}

.building {
  position: absolute;
  bottom: 0;
  width: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.building:nth-child(odd) {
  background: rgba(255, 255, 255, 0.15);
}

/* 动画 */
@keyframes earthRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes orbitRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 右侧登录表单区域 */
.right-section {
  flex: 1;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.login-container {
  width: 100%;
  max-width: 400px;
}

.login-form {
  background: white;
  padding: 2rem;
}

.form-title {
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  transition: all 0.2s;
}

.input-wrapper:focus-within {
  border-color: #007bff;
  background: white;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.input-icon {
  width: 20px;
  height: 20px;
  color: #6c757d;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.input-wrapper input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 1rem;
  color: #333;
}

.input-wrapper input::placeholder {
  color: #adb5bd;
}

.password-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  color: #6c757d;
  margin-left: 0.5rem;
}

.password-toggle svg {
  width: 18px;
  height: 18px;
}

.captcha-wrapper {
  padding-right: 0;
}

.captcha-image {
  margin-left: 0.5rem;
  cursor: pointer;
  border-left: 1px solid #e9ecef;
  padding-left: 0.5rem;
}

.captcha-image canvas {
  border-radius: 4px;
  background: #f0f0f0;
}

.login-btn {
  width: 100%;
  background: #007bff;
  color: white;
  border: none;
  padding: 0.875rem 1rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 1.5rem;
}

.login-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  text-decoration: none;
  font-size: 0.875rem;
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.2s;
}

.footer-link:hover {
  color: #007bff;
}

.footer-link svg {
  width: 16px;
  height: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-page {
    flex-direction: column;
  }

  .left-section {
    min-height: 40vh;
  }

  .right-section {
    min-height: 60vh;
  }

  .earth-container {
    width: 200px;
    height: 200px;
  }

  .earth {
    width: 100px;
    height: 100px;
    margin: 50px auto;
  }

  .orbit-1 {
    width: 140px;
    height: 140px;
    top: 30px;
    left: 30px;
  }

  .orbit-2 {
    width: 170px;
    height: 170px;
    top: 15px;
    left: 15px;
  }

  .orbit-3 {
    width: 200px;
    height: 200px;
  }
}
</style>
