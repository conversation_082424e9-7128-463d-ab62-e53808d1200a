{"name": "api-server", "version": "1.0.0", "description": "Backend API server with Node.js, Express, MySQL and JWT (Port: 8002)", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "mysql", "jwt", "api"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=20.0.0"}}