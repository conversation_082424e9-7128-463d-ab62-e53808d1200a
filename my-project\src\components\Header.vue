<template>
  <header class="header">
    <div class="header-container">
      <!-- Logo 区域 -->
      <div class="logo-section">
        <router-link to="/" class="logo-link">
          <img src="https://picsum.photos/40/40?random=1" alt="Logo" class="logo-img" />
          <span class="logo-text">My Project</span>
        </router-link>
      </div>

      <!-- 导航栏 -->
      <nav class="nav-section">
        <div class="nav-menu">
          <router-link 
            v-for="item in navItems" 
            :key="item.path"
            :to="item.path" 
            class="nav-item"
            :class="{ 'active': $route.path === item.path }"
          >
            {{ item.name }}
          </router-link>
        </div>
      </nav>

      <!-- 右侧操作区 -->
      <div class="actions-section">
        <!-- 搜索框 -->
        <div class="search-box">
          <input 
            type="text" 
            placeholder="搜索内容..." 
            class="search-input"
            v-model="searchQuery"
            @keyup.enter="handleSearch"
          />
          <button class="search-btn" @click="handleSearch">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </button>
        </div>

        <!-- 发布按钮 -->
        <button class="publish-btn" @click="handlePublish">发布</button>

        <!-- 登录按钮 -->
        <button class="login-btn" @click="handleLogin">登录</button>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const searchQuery = ref('')

// 导航项配置
const navItems = [
  { name: '主页', path: '/' },
  { name: '圈子', path: '/circle' },
  { name: '工具包', path: '/tools' },
  { name: '教程', path: '/tutorial' },
  { name: '问答', path: '/qa' },
  { name: '求职', path: '/jobs' }
]

// 事件处理函数
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    console.log('搜索:', searchQuery.value)
    // 这里可以添加搜索逻辑
  }
}

const handlePublish = () => {
  console.log('发布内容')
  // 这里可以添加发布逻辑
}

const handleLogin = () => {
  console.log('用户登录')
  // 这里可以添加登录逻辑
}
</script>

<style scoped>
.header {
  height: 60px;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.header-container {
  max-width: 1320px; /* 略大于内容区宽度 */
  margin: 0 auto;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
}

/* Logo 区域 */
.logo-section {
  flex-shrink: 0;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #1f2937;
}

.logo-img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 0.75rem;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
}

/* 导航区域 */
.nav-section {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 2rem;
}

.nav-menu {
  display: flex;
  gap: 2rem;
}

.nav-item {
  position: relative;
  padding: 1rem 0.5rem;
  color: #6b7280;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.nav-item:hover {
  color: #3b82f6;
}

.nav-item.active {
  color: #3b82f6;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #3b82f6;
  border-radius: 1px;
}

/* 右侧操作区 */
.actions-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
}

/* 搜索框 */
.search-box {
  display: flex;
  align-items: center;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.5rem;
  transition: border-color 0.2s ease;
}

.search-box:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input {
  border: none;
  background: transparent;
  outline: none;
  padding: 0 0.5rem;
  font-size: 0.875rem;
  width: 200px;
  color: #374151;
}

.search-input::placeholder {
  color: #9ca3af;
}

.search-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.search-btn:hover {
  color: #3b82f6;
}

/* 按钮样式 */
.publish-btn,
.login-btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.publish-btn {
  background-color: #3b82f6;
  color: white;
}

.publish-btn:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.login-btn {
  background-color: transparent;
  color: #374151;
  border: 1px solid #d1d5db;
}

.login-btn:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .header-container {
    max-width: 100%;
    padding: 0 1rem;
  }
  
  .nav-section {
    display: none; /* 移动端隐藏导航栏 */
  }
  
  .search-input {
    width: 120px;
  }
  
  .actions-section {
    gap: 0.5rem;
  }
  
  .logo-text {
    display: none; /* 移动端隐藏 logo 文字 */
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .header-container {
    max-width: 100%;
    padding: 0 1.5rem;
  }
  
  .nav-menu {
    gap: 1.5rem;
  }
  
  .search-input {
    width: 160px;
  }
}
</style>
