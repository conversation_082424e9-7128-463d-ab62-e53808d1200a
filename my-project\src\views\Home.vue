<template>
  <div class="home">
    <!-- 英雄区域 -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">欢迎来到知识乐园</h1>
        <p class="hero-subtitle">基于 Vue 3 + TypeScript + Vite 构建的现代化前端项目</p>
        <div class="hero-actions">
          <button class="btn btn-primary" @click="handleGetStarted">开始使用</button>
          <button class="btn btn-secondary" @click="handleLearnMore">了解更多</button>
        </div>
      </div>
    </div>

    <!-- 圈子分类 -->
    <div class="category-section">
      <div class="category-tabs">
        <button
          v-for="category in categories"
          :key="category.id"
          :class="['category-tab', { active: activeCategory === category.id }]"
          @click="activeCategory = category.id"
        >
          {{ category.name }}
        </button>
      </div>
    </div>

    <!-- 热门圈子 - 占据全宽 -->
    <div class="hot-circles-section">
      <div class="section-header">
        <h2 class="section-title">热门圈子</h2>
        <a href="#" class="more-link">更多圈子 ></a>
      </div>
      <div class="circles-grid">
        <div
          v-for="circle in hotCircles"
          :key="circle.id"
          class="circle-card"
        >
          <div class="circle-image">
            <img :src="circle.image" :alt="circle.name" />
            <div class="circle-badge" v-if="circle.isNew">NEW</div>
            <div class="circle-members">{{ circle.members }}</div>
          </div>
          <div class="circle-info">
            <h3 class="circle-name">{{ circle.name }}</h3>
            <p class="circle-desc">{{ circle.description }}</p>
            <div class="circle-stats">
              <span class="stat-item">
                <i class="icon-user"></i>
                {{ circle.memberCount }}
              </span>
              <span class="stat-item">
                <i class="icon-message"></i>
                {{ circle.postCount }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部两栏布局：圈子榜单 + 热门话题 -->
    <div class="bottom-section">
      <!-- 圈子榜单 -->
      <div class="ranking-section">
        <div class="section-header">
          <h2 class="section-title">圈子榜单</h2>
          <div class="ranking-tabs">
            <button class="ranking-tab active">日榜</button>
            <button class="ranking-tab">周榜</button>
            <button class="ranking-tab">月榜</button>
          </div>
        </div>
        <div class="ranking-list">
          <div
            v-for="(item, index) in rankingList"
            :key="item.id"
            class="ranking-item"
          >
            <div class="ranking-number">{{ index + 1 }}</div>
            <div class="ranking-avatar">
              <img :src="item.avatar" :alt="item.name" />
            </div>
            <div class="ranking-info">
              <div class="ranking-name">{{ item.name }}</div>
              <div class="ranking-desc">{{ item.description }}</div>
            </div>
            <div class="ranking-score">{{ item.score }}分</div>
          </div>
        </div>
      </div>

      <!-- 热门话题 -->
      <div class="hot-topics-section">
        <div class="section-header">
          <h2 class="section-title">热门话题</h2>
          <a href="#" class="more-link">更多话题 ></a>
        </div>
        <div class="topics-list">
          <div
            v-for="topic in hotTopics"
            :key="topic.id"
            class="topic-item"
          >
            <div class="topic-icon">
              <i :class="topic.icon"></i>
            </div>
            <div class="topic-info">
              <div class="topic-title">{{ topic.title }}</div>
              <div class="topic-stats">
                <span>{{ topic.views }}人浏览</span>
                <span>{{ topic.replies }}人回复</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 响应式数据
const activeCategory = ref('all')

// 分类数据
const categories = ref([
  { id: 'all', name: '全部' },
  { id: 'tech', name: '技术' },
  { id: 'design', name: '设计' },
  { id: 'product', name: '产品' },
  { id: 'career', name: '职场' },
  { id: 'life', name: '生活' }
])

// 热门圈子数据
const hotCircles = ref([
  {
    id: 1,
    name: '人工智能研究',
    description: '探讨AI技术发展与应用',
    image: 'https://picsum.photos/200/120?random=1',
    isNew: true,
    members: '1.2k',
    memberCount: '1.2k',
    postCount: '3.5k'
  },
  {
    id: 2,
    name: '前端技术交流',
    description: '分享前端开发经验与技巧',
    image: 'https://picsum.photos/200/120?random=2',
    isNew: false,
    members: '2.1k',
    memberCount: '2.1k',
    postCount: '5.2k'
  },
  {
    id: 3,
    name: '设计师圈',
    description: 'UI/UX设计师的创意分享',
    image: 'https://picsum.photos/200/120?random=3',
    isNew: true,
    members: '856',
    memberCount: '856',
    postCount: '1.8k'
  },
  {
    id: 4,
    name: '读书分享',
    description: '好书推荐与读书心得',
    image: 'https://picsum.photos/200/120?random=4',
    isNew: false,
    members: '1.5k',
    memberCount: '1.5k',
    postCount: '2.3k'
  },
  {
    id: 5,
    name: '摄影技巧',
    description: '摄影爱好者的技巧交流',
    image: 'https://picsum.photos/200/120?random=5',
    isNew: false,
    members: '987',
    memberCount: '987',
    postCount: '1.4k'
  },
  {
    id: 6,
    name: '创业讨论',
    description: '创业经验与商业思考',
    image: 'https://picsum.photos/200/120?random=6',
    isNew: true,
    members: '743',
    memberCount: '743',
    postCount: '1.1k'
  },
  {
    id: 7,
    name: '健身运动',
    description: '健身知识与运动经验',
    image: 'https://picsum.photos/200/120?random=7',
    isNew: false,
    members: '1.3k',
    memberCount: '1.3k',
    postCount: '2.7k'
  },
  {
    id: 8,
    name: '美食烹饪',
    description: '美食制作与烹饪技巧',
    image: 'https://picsum.photos/200/120?random=8',
    isNew: true,
    members: '2.4k',
    memberCount: '2.4k',
    postCount: '4.1k'
  }
])

// 圈子榜单数据
const rankingList = ref([
  {
    id: 1,
    name: '人工智能研究',
    description: '1.2k成员 · 今日新增',
    avatar: 'https://picsum.photos/40/40?random=11',
    score: '95'
  },
  {
    id: 2,
    name: '前端技术交流',
    description: '2.1k成员 · 活跃',
    avatar: 'https://picsum.photos/40/40?random=12',
    score: '92'
  },
  {
    id: 3,
    name: '设计师圈',
    description: '856成员 · 热门',
    avatar: 'https://picsum.photos/40/40?random=13',
    score: '89'
  },
  {
    id: 4,
    name: '创业讨论',
    description: '743成员 · 上升',
    avatar: 'https://picsum.photos/40/40?random=14',
    score: '87'
  },
  {
    id: 5,
    name: '美食烹饪',
    description: '2.4k成员 · 热门',
    avatar: 'https://picsum.photos/40/40?random=15',
    score: '85'
  }
])

// 热门话题数据
const hotTopics = ref([
  {
    id: 1,
    title: '2024年前端技术趋势预测',
    views: '1.2k',
    replies: '156',
    icon: 'icon-trend'
  },
  {
    id: 2,
    title: 'Vue 3.4 新特性详解',
    views: '856',
    replies: '89',
    icon: 'icon-code'
  },
  {
    id: 3,
    title: '如何提高代码质量',
    views: '2.1k',
    replies: '234',
    icon: 'icon-quality'
  },
  {
    id: 4,
    title: '远程工作的优缺点分析',
    views: '1.5k',
    replies: '178',
    icon: 'icon-work'
  },
  {
    id: 5,
    title: '设计系统搭建经验分享',
    views: '743',
    replies: '67',
    icon: 'icon-design'
  }
])

// 事件处理函数
const handleGetStarted = () => {
  console.log('开始使用')
}

const handleLearnMore = () => {
  console.log('了解更多')
}
</script>

<style scoped>
.home {
  padding: 0;
  background-color: #f8fafc;
}

/* 英雄区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 4rem 2rem;
  text-align: center;
  color: white;
  margin-bottom: 2rem;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #ffffff;
  color: #667eea;
}

.btn-primary:hover {
  background-color: #f8fafc;
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background-color: white;
  color: #667eea;
  transform: translateY(-2px);
}

/* 分类标签 */
.category-section {
  background: white;
  padding: 1rem 0;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.category-tabs {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.category-tab {
  padding: 0.5rem 1rem;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 20px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.category-tab.active {
  background: #3b82f6;
  color: white;
}

.category-tab:hover {
  background: #e5e7eb;
}

.category-tab.active:hover {
  background: #2563eb;
}

/* 热门圈子 - 全宽 */
.hot-circles-section {
  background: white;
  padding: 2rem;
  margin-bottom: 2rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.more-link {
  color: #3b82f6;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
}

.more-link:hover {
  color: #2563eb;
}

.circles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  width: 100%;
}

.circle-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s;
  cursor: pointer;
}

.circle-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.circle-image {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.circle-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.circle-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #ef4444;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.circle-members {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.circle-info {
  padding: 1rem;
}

.circle-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.circle-desc {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.circle-stats {
  display: flex;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #9ca3af;
  font-size: 0.75rem;
}

.icon-user::before {
  content: "👥";
}

.icon-message::before {
  content: "💬";
}

/* 底部两栏布局 */
.bottom-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
  width: 100%;
}

/* 圈子榜单 */
.ranking-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ranking-tabs {
  display: flex;
  gap: 0.5rem;
}

.ranking-tab {
  padding: 0.5rem 1rem;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.ranking-tab.active {
  background: #3b82f6;
  color: white;
}

.ranking-list {
  margin-top: 1.5rem;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 50%;
  font-size: 0.875rem;
  font-weight: 600;
  flex-shrink: 0;
}

.ranking-item:nth-child(1) .ranking-number {
  background: #fbbf24;
  color: white;
}

.ranking-item:nth-child(2) .ranking-number {
  background: #9ca3af;
  color: white;
}

.ranking-item:nth-child(3) .ranking-number {
  background: #f59e0b;
  color: white;
}

.ranking-avatar {
  flex-shrink: 0;
}

.ranking-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.ranking-desc {
  color: #6b7280;
  font-size: 0.875rem;
}

.ranking-score {
  color: #3b82f6;
  font-weight: 600;
  font-size: 0.875rem;
}

/* 热门话题 */
.hot-topics-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.topics-list {
  margin-top: 1.5rem;
}

.topic-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s;
}

.topic-item:last-child {
  border-bottom: none;
}

.topic-item:hover {
  background-color: #f9fafb;
}

.topic-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 8px;
  flex-shrink: 0;
}

.icon-trend::before { content: "📈"; }
.icon-code::before { content: "💻"; }
.icon-quality::before { content: "⭐"; }
.icon-work::before { content: "💼"; }
.icon-design::before { content: "🎨"; }

.topic-info {
  flex: 1;
}

.topic-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.topic-stats {
  display: flex;
  gap: 1rem;
  color: #6b7280;
  font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-section {
    padding: 3rem 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 200px;
  }

  .category-tabs {
    padding: 0 1rem;
  }

  .circles-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .bottom-section {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .hot-circles-section,
  .ranking-section,
  .hot-topics-section {
    padding: 1.5rem;
    margin: 0 1rem;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .circles-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .hot-circles-section,
  .ranking-section,
  .hot-topics-section {
    padding: 1.5rem;
  }
}

@media (min-width: 1025px) {
  .circles-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
