<template>
  <div class="home">
    <div class="hero">
      <h1 class="hero-title">欢迎来到 My Project</h1>
      <p class="hero-subtitle">基于 Vue 3 + TypeScript + Vite 构建的现代化前端项目</p>
      <div class="hero-actions">
        <button class="btn btn-primary" @click="handleGetStarted">开始使用</button>
        <button class="btn btn-secondary" @click="handleLearnMore">了解更多</button>
      </div>
    </div>
    
    <div class="features">
      <h2 class="features-title">技术特性</h2>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">⚡</div>
          <h3>快速开发</h3>
          <p>基于 Vite 的极速热重载，提升开发效率</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">🔧</div>
          <h3>TypeScript</h3>
          <p>完整的类型支持，更好的开发体验</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">🎨</div>
          <h3>Vue 3</h3>
          <p>最新的 Vue 3 Composition API</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">📱</div>
          <h3>响应式设计</h3>
          <p>适配各种屏幕尺寸的响应式布局</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const handleGetStarted = () => {
  console.log('开始使用')
}

const handleLearnMore = () => {
  console.log('了解更多')
}
</script>

<style scoped>
.home {
  padding: 0;
}

.hero {
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  margin-bottom: 4rem;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #ffffff;
  color: #667eea;
}

.btn-primary:hover {
  background-color: #f8fafc;
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background-color: white;
  color: #667eea;
  transform: translateY(-2px);
}

.features {
  padding: 2rem 0;
}

.features-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.2s;
}

.feature-card:hover {
  transform: translateY(-4px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1f2937;
}

.feature-card p {
  color: #6b7280;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 200px;
  }
}
</style>
